import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Search, PlusCircle, Edit, Trash2, Download, RefreshCw, Filter, MoreHorizontal, Eye, Copy, FileText, BarChart3, CheckSquare, XSquare, Package, AlertTriangle, AlertCircle, Calendar, Check, Clock, ChevronLeft, ChevronRight, HelpCircle, Layers, FileX, Settings, TrendingUp, Activity } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { apiService } from '@/services/base-api';

// --- 类型定义 ---

// 税收编码数据结构
interface TaxCode {
  id: number;
  categoryName: string;
  categoryCode: string;
  categoryShortCode?: string;
  isSummary: boolean;
  isActive: boolean;
  shortName?: string;
  description?: string;
  keywords?: string;
  createdAt: string;
  updatedAt: string;
}

// 筛选条件
interface TaxCodeFilters {
  search: string;
  isActive: 'true' | 'false' | 'all';
  isSummary: 'true' | 'false' | 'all';
}

// 统计数据
interface TaxCodeStats {
  total: number;
  active: number;
  inactive: number;
  summary: number;
}

// 对话框状态
type DialogState =
  | { type: 'closed' }
  | { type: 'add' }
  | { type: 'edit'; taxCode: TaxCode }
  | { type: 'view'; taxCode: TaxCode }
  | { type: 'delete'; taxCodeId: number }
  | { type: 'batchDelete'; ids: number[] };

// --- 初始值 ---

// 初始表单数据
const initialFormData = {
  categoryName: '',
  categoryCode: '',
  categoryShortCode: '',
  isSummary: false,
  isActive: true,
  shortName: '',
  description: '',
  keywords: '',
};

// --- 组件接口 ---
type ActionType = 'view' | 'edit' | 'delete';

// 通用组件属性
interface ComponentProps {
  title: string;
  value: number | string;
  icon: React.ElementType;
  colorClass: string;

  // 统计数据
  stats?: TaxCodeStats;

  // 事件处理
  onAddNew?: () => void;
  onFilterChange?: (key: keyof TaxCodeFilters, value: string) => void;
  onRefresh?: () => void;
  onSearch?: (value: string) => void;
  onSelect?: () => void;
  onAction?: (type: ActionType, taxCode: TaxCode) => void;
  onSelectAll?: (checked: boolean | 'indeterminate') => void;
  onSelectItem?: (id: number) => void;
  onClose?: () => void;
  onSave?: (data: typeof initialFormData, id: number | null) => void;
  onEdit?: (taxCode: TaxCode) => void;
  onConfirm?: () => void;

  // 数据
  filters?: TaxCodeFilters;
  taxCode?: TaxCode;
  taxCodes?: TaxCode[];
  loading?: boolean;
  selectedIds?: number[];
  isSelected?: boolean;
  dialogState?: DialogState;
}

// 表格行组件属性
interface TaxCodeTableRowProps {
  taxCode: TaxCode;
  isSelected: boolean;
  onSelect: () => void;
  onAction?: (type: ActionType, taxCode: TaxCode) => void;
}

// --- UI 组件 ---

// 统计卡片组件
const StatCard: React.FC<ComponentProps> = ({ title, value, icon: Icon, colorClass }) => (
  <Card className="hover:shadow-md transition-shadow duration-200">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className={`text-3xl font-bold ${colorClass}`}>{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClass.replace('text-', 'bg-').replace('-600', '-100')}`}>
          <Icon className={`h-6 w-6 ${colorClass}`} />
        </div>
      </div>
    </CardContent>
  </Card>
);

// 页面标题组件
const PageHeader: React.FC<ComponentProps> = ({ onAddNew, stats }) => (
  <div className="space-y-6">
    {/* 主标题区域 */}
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
      <div className="space-y-1">
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Package className="h-8 w-8 text-blue-600" />
          </div>
          商品和服务税收编码
        </h1>
        <p className="text-lg text-muted-foreground">管理商品和服务的税收分类编码，确保合规性和准确性</p>
      </div>
      <Button onClick={onAddNew} size="lg" className="bg-blue-600 hover:bg-blue-700 shadow-md">
        <PlusCircle className="h-5 w-5 mr-2" />
        新增编码
      </Button>
    </div>

    {/* 统计卡片区域 */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="总编码数"
        value={stats?.total || 0}
        icon={Package}
        colorClass="text-blue-600"
      />
      <StatCard
        title="可用编码"
        value={stats?.active || 0}
        icon={Check}
        colorClass="text-green-600"
      />
      <StatCard
        title="禁用编码"
        value={stats?.inactive || 0}
        icon={XSquare}
        colorClass="text-red-600"
      />
      <StatCard
        title="汇总项"
        value={stats?.summary || 0}
        icon={Layers}
        colorClass="text-purple-600"
      />
    </div>
  </div>
);

// 筛选栏组件
const FilterBar: React.FC<ComponentProps> = ({ filters, onFilterChange, onRefresh, onSearch }) => (
  <Card className="shadow-sm border-0 bg-white">
    <CardHeader className="pb-4">
      <CardTitle className="text-lg font-semibold flex items-center gap-2">
        <Filter className="h-5 w-5 text-blue-600" />
        搜索与筛选
      </CardTitle>
      <CardDescription>快速查找和筛选税收编码</CardDescription>
    </CardHeader>
    <CardContent className="space-y-6">
      {/* 搜索栏 */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索编码名称、编号或关键字..."
          value={filters?.search || ''}
          onChange={(e) => onSearch?.(e.target.value)}
          className="pl-10 h-11 bg-gray-50 border-gray-200 focus:bg-white transition-colors"
        />
      </div>

      {/* 筛选选项 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">状态筛选</Label>
          <Select
            value={filters?.isActive || 'all'}
            onValueChange={(v: 'true' | 'false' | 'all') => onFilterChange?.('isActive', v)}
          >
            <SelectTrigger className="bg-gray-50 border-gray-200 focus:bg-white">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="true">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  可用
                </div>
              </SelectItem>
              <SelectItem value="false">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  禁用
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">类型筛选</Label>
          <Select
            value={filters?.isSummary || 'all'}
            onValueChange={(v: 'true' | 'false' | 'all') => onFilterChange?.('isSummary', v)}
          >
            <SelectTrigger className="bg-gray-50 border-gray-200 focus:bg-white">
              <SelectValue placeholder="选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="true">
                <div className="flex items-center gap-2">
                  <Layers className="w-4 h-4 text-purple-600" />
                  汇总项
                </div>
              </SelectItem>
              <SelectItem value="false">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-blue-600" />
                  明细项
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">操作</Label>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onRefresh} className="flex-1">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

// 表格行组件
const TaxCodeTableRow: React.FC<TaxCodeTableRowProps> = ({ taxCode, isSelected, onSelect, onAction }) => (
  <TableRow className={`group transition-all duration-200 ${isSelected ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'}`}>
    <TableCell className="py-4 w-12">
      <Checkbox
        checked={isSelected}
        onCheckedChange={onSelect}
        className={`transition-colors ${isSelected ? 'border-blue-500 data-[state=checked]:bg-blue-600' : ''}`}
      />
    </TableCell>
    <TableCell className="py-4">
      <div className="font-mono text-sm font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md inline-block">
        {taxCode?.categoryCode}
      </div>
    </TableCell>
    <TableCell className="py-4 max-w-xs">
      <div className="space-y-1">
        <div className="font-medium text-gray-900 truncate" title={taxCode?.categoryName}>
          {taxCode?.categoryName}
        </div>
        {taxCode?.shortName && (
          <div className="text-xs text-muted-foreground bg-gray-100 px-2 py-0.5 rounded-full inline-block">
            {taxCode?.shortName}
          </div>
        )}
      </div>
    </TableCell>
    <TableCell className="py-4">
      <Badge
        variant={taxCode?.isSummary ? 'default' : 'secondary'}
        className={`px-3 py-1 font-medium ${
          taxCode?.isSummary
            ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
        }`}
      >
        <div className="flex items-center gap-1">
          {taxCode?.isSummary ? <Layers className="h-3 w-3" /> : <FileText className="h-3 w-3" />}
          {taxCode?.isSummary ? '汇总项' : '明细项'}
        </div>
      </Badge>
    </TableCell>
    <TableCell className="py-4">
      <Badge
        variant={taxCode?.isActive ? 'default' : 'destructive'}
        className={`px-3 py-1 font-medium ${
          taxCode?.isActive
            ? 'bg-green-100 text-green-700 hover:bg-green-200'
            : 'bg-red-100 text-red-700 hover:bg-red-200'
        }`}
      >
        <div className="flex items-center gap-1">
          {taxCode?.isActive ? <Check className="h-3 w-3" /> : <XSquare className="h-3 w-3" />}
          {taxCode?.isActive ? '可用' : '禁用'}
        </div>
      </Badge>
    </TableCell>
    <TableCell className="py-4 max-w-xs">
      {taxCode?.keywords ? (
        <div className="flex flex-wrap gap-1">
          {taxCode.keywords.split(',').slice(0, 2).map((keyword, index) => (
            <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">
              {keyword.trim()}
            </span>
          ))}
          {taxCode.keywords.split(',').length > 2 && (
            <span className="text-xs text-muted-foreground">+{taxCode.keywords.split(',').length - 2}</span>
          )}
        </div>
      ) : (
        <span className="text-muted-foreground text-sm">-</span>
      )}
    </TableCell>
    <TableCell className="py-4 w-32">
      <div className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onAction?.('view', taxCode!)}
          className="h-8 w-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50 transition-colors"
          title="查看详情"
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onAction?.('edit', taxCode!)}
          className="h-8 w-8 text-gray-500 hover:text-green-600 hover:bg-green-50 transition-colors"
          title="编辑"
        >
          <Edit className="h-4 w-4" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(taxCode?.categoryCode || '')}>
              <Copy className="mr-2 h-4 w-4" />
              复制编号
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600 focus:bg-red-50"
              onClick={() => onAction?.('delete', taxCode!)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </TableCell>
  </TableRow>
);

// 表格组件
const TaxCodeTable: React.FC<ComponentProps> = ({ taxCodes = [], loading, selectedIds = [], onSelectAll, onSelectItem, onAction, onAddNew }) => {
  const isAllSelected = taxCodes.length > 0 && selectedIds.length === taxCodes.length;
  const isPartialSelected = selectedIds.length > 0 && !isAllSelected;

  if (loading) {
    return (
      <Card className="shadow-sm">
        <CardContent className="p-12">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative">
              <div className="h-12 w-12 rounded-full border-4 border-blue-200 border-t-blue-600 animate-spin"></div>
              <Package className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-5 w-5 text-blue-600" />
            </div>
            <div className="text-center space-y-2">
              <p className="text-lg font-medium text-gray-900">加载中...</p>
              <p className="text-sm text-muted-foreground">正在获取税收编码数据</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (taxCodes.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardContent className="p-12">
          <div className="text-center space-y-6">
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center">
              <FileX className="h-12 w-12 text-gray-400" />
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold text-gray-900">暂无税收编码</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                没有找到符合条件的税收编码数据，您可以创建第一个税收编码
              </p>
            </div>
            <Button
              onClick={onAddNew}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 shadow-md"
            >
              <PlusCircle className="mr-2 h-5 w-5" />
              创建第一个税收编码
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">税收编码列表</CardTitle>
            <CardDescription>共 {taxCodes.length} 条记录</CardDescription>
          </div>
          {selectedIds.length > 0 && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-700">
              已选择 {selectedIds.length} 项
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-gray-50/50">
              <TableRow className="hover:bg-gray-50/50 border-b">
                <TableHead className="w-12 py-4 pl-6">
                  <Checkbox
                    checked={isPartialSelected ? 'indeterminate' : isAllSelected}
                    onCheckedChange={onSelectAll}
                    className="data-[state=indeterminate]:bg-blue-600 data-[state=checked]:bg-blue-600"
                  />
                </TableHead>
                <TableHead className="py-4 font-semibold text-gray-700">编号</TableHead>
                <TableHead className="py-4 font-semibold text-gray-700">名称</TableHead>
                <TableHead className="py-4 font-semibold text-gray-700">类型</TableHead>
                <TableHead className="py-4 font-semibold text-gray-700">状态</TableHead>
                <TableHead className="py-4 font-semibold text-gray-700">关键字</TableHead>
                <TableHead className="w-32 py-4 font-semibold text-gray-700 text-right pr-6">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {taxCodes.map((code) => (
                <TaxCodeTableRow
                  key={code.id}
                  taxCode={code}
                  isSelected={selectedIds.includes(code.id)}
                  onSelect={() => onSelectItem?.(code.id)}
                  onAction={onAction}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

// 新增/编辑对话框
const UpsertTaxCodeDialog: React.FC<ComponentProps> = ({ dialogState, onClose, onSave }) => {
  const isEdit = dialogState?.type === 'edit';
  const open = dialogState?.type === 'add' || dialogState?.type === 'edit';
  const [formData, setFormData] = useState(initialFormData);

  useEffect(() => {
    if (dialogState?.type === 'edit' && dialogState.taxCode) {
      const { taxCode } = dialogState;
      setFormData({
        categoryName: taxCode.categoryName,
        categoryCode: taxCode.categoryCode,
        categoryShortCode: taxCode.categoryShortCode || '',
        isSummary: taxCode.isSummary,
        isActive: taxCode.isActive,
        shortName: taxCode.shortName || '',
        description: taxCode.description || '',
        keywords: taxCode.keywords || ''
      });
    } else {
      setFormData(initialFormData);
    }
  }, [dialogState]);

  const handleChange = (field: keyof typeof initialFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    if (onSave && dialogState) {
      onSave(formData, isEdit && dialogState.type === 'edit' ? dialogState.taxCode.id : null);
    }
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4 pb-6 border-b">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-full ${isEdit ? 'bg-green-100' : 'bg-blue-100'}`}>
              {isEdit ? (
                <Edit className={`h-6 w-6 ${isEdit ? 'text-green-600' : 'text-blue-600'}`} />
              ) : (
                <PlusCircle className="h-6 w-6 text-blue-600" />
              )}
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">
                {isEdit ? '编辑税收编码' : '新增税收编码'}
              </DialogTitle>
              <p className="text-muted-foreground mt-1">
                {isEdit ? '修改现有税收编码的详细信息' : '添加新的税收编码到系统中'}
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryCode" className="text-sm font-medium">
                  编号 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="categoryCode"
                  value={formData.categoryCode}
                  onChange={(e) => handleChange('categoryCode', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  disabled={isEdit}
                  placeholder="例如: GST001"
                />
                {isEdit && (
                  <p className="text-xs text-muted-foreground">编码创建后不可修改</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryName" className="text-sm font-medium">
                  名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="categoryName"
                  value={formData.categoryName}
                  onChange={(e) => handleChange('categoryName', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: 标准税率"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoryShortCode" className="text-sm font-medium">简码</Label>
                <Input
                  id="categoryShortCode"
                  value={formData.categoryShortCode}
                  onChange={(e) => handleChange('categoryShortCode', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: STD"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="shortName" className="text-sm font-medium">简称</Label>
                <Input
                  id="shortName"
                  value={formData.shortName}
                  onChange={(e) => handleChange('shortName', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                  placeholder="例如: 标准"
                />
              </div>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-600" />
              详细信息
            </h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keywords" className="text-sm font-medium">关键字</Label>
                <Input
                  id="keywords"
                  value={formData.keywords}
                  onChange={(e) => handleChange('keywords', e.target.value)}
                  placeholder="用逗号分隔关键字，例如: 标准,普通,一般"
                  className="bg-gray-50 border-gray-200 focus:bg-white"
                />
                <p className="text-xs text-muted-foreground">关键字有助于快速搜索和分类</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">说明</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  className="bg-gray-50 border-gray-200 focus:bg-white min-h-[100px]"
                  placeholder="请输入详细说明..."
                />
              </div>
            </div>
          </div>

          {/* 配置选项 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-600" />
              配置选项
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4 border-purple-200 bg-purple-50">
                <div className="flex items-center gap-3">
                  <Switch
                    id="isSummary"
                    checked={formData.isSummary}
                    onCheckedChange={(c) => handleChange('isSummary', c)}
                    className="data-[state=checked]:bg-purple-600"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="isSummary" className="text-sm font-medium text-purple-900">设为汇总项</Label>
                    <p className="text-xs text-purple-700">汇总项可以包含多个子项目</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4 border-green-200 bg-green-50">
                <div className="flex items-center gap-3">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(c) => handleChange('isActive', c)}
                    className="data-[state=checked]:bg-green-600"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="isActive" className="text-sm font-medium text-green-900">启用状态</Label>
                    <p className="text-xs text-green-700">启用后可在系统中使用</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.categoryCode || !formData.categoryName}
            className={`${isEdit ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            {isEdit ? (
              <>
                <Edit className="h-4 w-4 mr-2" />
                保存修改
              </>
            ) : (
              <>
                <PlusCircle className="h-4 w-4 mr-2" />
                创建编码
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 查看详情对话框
const ViewTaxCodeDialog: React.FC<ComponentProps> = ({ dialogState, onClose, onEdit }) => {
  if (dialogState?.type !== 'view' || !dialogState.taxCode) return null;
  const { taxCode } = dialogState;

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4 pb-6 border-b">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Eye className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold">税收编码详情</DialogTitle>
              <p className="text-muted-foreground mt-1">查看税收编码的详细信息</p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Package className="h-5 w-5 text-blue-600" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">编号</Label>
                  <div className="font-mono text-lg font-bold text-blue-700 bg-blue-50 px-3 py-2 rounded-md">
                    {taxCode.categoryCode}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">名称</Label>
                  <div className="text-lg font-semibold text-gray-900">
                    {taxCode.categoryName}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">简码</Label>
                  <div className="text-base text-gray-700">
                    {taxCode.categoryShortCode || <span className="text-muted-foreground">未设置</span>}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">简称</Label>
                  <div className="text-base text-gray-700">
                    {taxCode.shortName || <span className="text-muted-foreground">未设置</span>}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-600" />
              详细信息
            </h3>
            <div className="space-y-4">
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">关键字</Label>
                  <div>
                    {taxCode.keywords ? (
                      <div className="flex flex-wrap gap-2">
                        {taxCode.keywords.split(',').map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-700">
                            {keyword.trim()}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">未设置关键字</span>
                    )}
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">说明</Label>
                  <div className="text-gray-700 min-h-[60px] whitespace-pre-wrap">
                    {taxCode.description || <span className="text-muted-foreground">未添加说明</span>}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 状态信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              状态信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className={`p-4 border-2 ${taxCode.isSummary ? 'border-purple-200 bg-purple-50' : 'border-gray-200 bg-gray-50'}`}>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${taxCode.isSummary ? 'bg-purple-100' : 'bg-gray-100'}`}>
                    {taxCode.isSummary ? (
                      <Layers className="h-5 w-5 text-purple-600" />
                    ) : (
                      <FileText className="h-5 w-5 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {taxCode.isSummary ? '汇总项' : '明细项'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {taxCode.isSummary ? '此编码为汇总项目' : '此编码为明细项目'}
                    </p>
                  </div>
                </div>
              </Card>
              <Card className={`p-4 border-2 ${taxCode.isActive ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${taxCode.isActive ? 'bg-green-100' : 'bg-red-100'}`}>
                    {taxCode.isActive ? (
                      <Check className="h-5 w-5 text-green-600" />
                    ) : (
                      <XSquare className="h-5 w-5 text-red-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {taxCode.isActive ? '已启用' : '已禁用'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {taxCode.isActive ? '此编码可在系统中使用' : '此编码已被禁用'}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5 text-gray-600" />
              时间信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">创建时间</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(taxCode.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">更新时间</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(taxCode.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
          <Button onClick={() => onEdit?.(taxCode)} className="bg-blue-600 hover:bg-blue-700">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 删除确认对话框
const DeleteConfirmationDialog: React.FC<ComponentProps> = ({ dialogState, onClose, onConfirm }) => {
  const open = dialogState?.type === 'delete' || dialogState?.type === 'batchDelete';
  if (!open) return null;

  const isBatch = dialogState?.type === 'batchDelete';
  const count = isBatch && dialogState?.type === 'batchDelete' ? dialogState.ids.length : 1;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <Trash2 className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <DialogTitle className="text-xl font-bold text-gray-900">
              {isBatch ? `删除 ${count} 个税收编码` : '删除税收编码'}
            </DialogTitle>
            <p className="text-muted-foreground mt-2">
              此操作不可撤销，请确认您的选择
            </p>
          </div>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="text-center">
            <p className="text-gray-700">
              您确定要删除 <span className="font-semibold text-red-600">{count}</span> 个税收编码吗？
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              删除后数据将无法恢复
            </p>
          </div>

          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="font-medium text-amber-900">重要提醒</p>
                  <p className="text-sm text-amber-800">
                    {isBatch
                      ? '删除这些税收编码可能会影响已使用这些编码的发票、报表和其他相关数据。'
                      : '删除此税收编码可能会影响已使用此编码的发票、报表和其他相关数据。'}
                  </p>
                  <p className="text-sm text-amber-800 font-medium">
                    请确保在删除前已做好相应的数据备份和处理。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            className="flex-1 bg-red-600 hover:bg-red-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


// --- 主组件 ---
const TaxCodeManagement: React.FC = () => {
  // 状态管理
  const [taxCodes, setTaxCodes] = useState<TaxCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<TaxCodeFilters>({ search: '', isActive: 'all', isSummary: 'all' });
  const [pagination, setPagination] = useState({ page: 1, pageSize: 25, total: 0, totalPages: 1 });
  const [stats, setStats] = useState<TaxCodeStats>({ total: 0, active: 0, inactive: 0, summary: 0 });
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [dialogState, setDialogState] = useState<DialogState>({ type: 'closed' });

  // 获取税收编码数据
  const fetchData = useCallback(async (isRefresh = false) => {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: filters.search || undefined,
        isActive: filters.isActive === 'all' ? undefined : filters.isActive,
        isSummary: filters.isSummary === 'all' ? undefined : filters.isSummary,
        ...(isRefresh && { _t: Date.now() }),
      };
      const response = await apiService.get<{ data: TaxCode[], pagination: typeof pagination }>('/admin/tax-codes', params);
      if (response.success && response.data) {
        setTaxCodes(response.data.data);
        setPagination(p => ({ ...p, ...response.pagination }));
      } else {
        throw new Error(response.error || '获取数据失败');
      }
    } catch (error) {
      toast({ title: "加载失败", description: String(error), variant: "destructive" });
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, filters]);

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await apiService.get<TaxCodeStats>('/admin/tax-codes/stats');
      if (response.success) setStats(response.data);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  }, []);

  // 初始加载和依赖变化时重新获取数据
  useEffect(() => {
    fetchData();
    fetchStats();
  }, [fetchData, fetchStats]);

  // 处理筛选条件变化
  const handleFilterChange = useCallback((key: keyof TaxCodeFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(p => ({ ...p, page: 1 }));
  }, []);
  
  // 处理搜索，使用防抖
  const handleSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (value: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        handleFilterChange('search', value);
      }, 300);
    };
  }, [handleFilterChange]);

  // 处理全选/取消全选
  const handleSelectAll = useCallback((checked: boolean | 'indeterminate') => {
    setSelectedIds(checked === true ? taxCodes.map(c => c.id) : []);
  }, [taxCodes]);

  // 处理单个选择
  const handleSelectItem = useCallback((id: number) => {
    setSelectedIds(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  }, []);

  // 保存税收编码（新增或编辑）
  const handleSave = async (data: typeof initialFormData, id: number | null) => {
    try {
      const response = id
        ? await apiService.put(`/admin/tax-codes/${id}`, data)
        : await apiService.post('/admin/tax-codes', data);

      if (response.success) {
        toast({ title: "保存成功" });
        setDialogState({ type: 'closed' });
        fetchData(true);
        fetchStats();
      } else {
        throw new Error(response.error || '保存失败');
      }
    } catch (error) {
      toast({ title: "保存失败", description: String(error), variant: "destructive" });
    }
  };

  // 删除税收编码（单个或批量）
  const handleDelete = async () => {
    if (dialogState.type !== 'delete' && dialogState.type !== 'batchDelete') return;
    const idsToDelete = dialogState.type === 'delete' ? [dialogState.taxCodeId] : dialogState.ids;
    
    try {
      const response = idsToDelete.length > 1
        ? await apiService.post('/admin/tax-codes/batch-delete', { ids: idsToDelete })
        : await apiService.delete(`/admin/tax-codes/${idsToDelete[0]}`);

      if (response.success) {
        toast({ title: "删除成功" });
        setDialogState({ type: 'closed' });
        setSelectedIds([]);
        fetchData(true);
        fetchStats();
      } else {
        throw new Error(response.error || '删除失败');
      }
    } catch (error) {
      toast({ title: "删除失败", description: String(error), variant: "destructive" });
    }
  };

  // 处理表格行操作
  const handleTableAction = useCallback((type: 'view' | 'edit' | 'delete', taxCode: TaxCode) => {
    if (type === 'view') setDialogState({ type: 'view', taxCode });
    if (type === 'edit') setDialogState({ type: 'edit', taxCode });
    if (type === 'delete') setDialogState({ type: 'delete', taxCodeId: taxCode.id });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* 页面标题和统计 */}
        <PageHeader
          onAddNew={() => setDialogState({ type: 'add' })}
          stats={stats}
        />

        {/* 搜索和筛选 */}
        <FilterBar
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={() => fetchData(true)}
          onSearch={handleSearch}
        />

        {/* 批量操作栏 */}
        {selectedIds.length > 0 && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-amber-100 rounded-full">
                    <CheckSquare className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <p className="font-medium text-amber-900">已选择 {selectedIds.length} 项</p>
                    <p className="text-sm text-amber-700">您可以对选中的项目执行批量操作</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedIds([])}
                    className="border-amber-300 text-amber-700 hover:bg-amber-100"
                  >
                    取消选择
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setDialogState({ type: 'batchDelete', ids: selectedIds })}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 数据表格 */}
        <TaxCodeTable
          taxCodes={taxCodes}
          loading={loading}
          selectedIds={selectedIds}
          onSelectAll={handleSelectAll}
          onSelectItem={handleSelectItem}
          onAction={handleTableAction}
          onAddNew={() => setDialogState({ type: 'add' })}
        />

        {/* 分页控制 */}
        {pagination.totalPages > 1 && (
          <Card className="shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  显示第 {(pagination.page - 1) * pagination.pageSize + 1} - {Math.min(pagination.page * pagination.pageSize, pagination.total)} 条，
                  共 {pagination.total} 条记录
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(p => ({ ...p, page: p.page - 1 }))}
                    disabled={pagination.page === 1}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    上一页
                  </Button>
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <Button
                          key={pageNum}
                          variant={pagination.page === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPagination(p => ({ ...p, page: pageNum }))}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(p => ({ ...p, page: p.page + 1 }))}
                    disabled={pagination.page === pagination.totalPages}
                  >
                    下一页
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* 对话框组件 */}
      <UpsertTaxCodeDialog
        dialogState={dialogState}
        onClose={() => setDialogState({ type: 'closed' })}
        onSave={handleSave}
      />
      <ViewTaxCodeDialog
        dialogState={dialogState}
        onClose={() => setDialogState({ type: 'closed' })}
        onEdit={(taxCode) => setDialogState({ type: 'edit', taxCode })}
      />
      <DeleteConfirmationDialog
        dialogState={dialogState}
        onClose={() => setDialogState({ type: 'closed' })}
        onConfirm={handleDelete}
      />
    </div>
  );
};

export default TaxCodeManagement;